/**
 * Document Types
 * Định nghĩa các types cho module Documents
 */

// Enums
export enum DocumentType {
  POLICY = 'POLICY',
  PROCEDURE = 'PROCEDURE',
  MANUAL = 'MANUAL',
  FORM = 'FORM',
  TEMPLATE = 'TEMPLATE',
  REPORT = 'REPORT',
  CONTRACT = 'CONTRACT',
  PRESENTATION = 'PRESENTATION',
  SPREADSHEET = 'SPREADSHEET',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  OTHER = 'OTHER',
}

export enum ProcessingStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export enum PermissionLevel {
  READ = 'read',
  write = 'write',
  admin = 'admin',
}

// Document Response DTO
export interface DocumentResponseDto {
  id: number;
  title: string;
  description: string | null;
  documentType: DocumentType;
  fileName: string;
  fileSize: number;
  mimeType: string;
  folderId: number | null;
  s3Key: string;
  s3Bucket: string | null;
  openaiFileId: string | null;
  openaiVectorStoreId: string | null;
  processingStatus: ProcessingStatus;
  processingError: string | null;
  lastProcessedAt: number | null;
  retryCount: number;
  contentHash: string | null;
  pageCount: number | null;
  wordCount: number | null;
  language: string;
  isActive: boolean;
  isPublic: boolean;
  version: number;
  tags: string[] | null;
  metadata: Record<string, any> | null;
  createdAt: number;
  updatedAt: number | null;
  createdBy: number;
  updatedBy: number | null;
  tenantId: number;
  
  // Computed properties
  fileSizeMB: number;
  isProcessed: boolean;
  isProcessingFailed: boolean;
  canRetry: boolean;
  fileExtension: string;
  isPDF: boolean;
  isWordDocument: boolean;
  isTextFile: boolean;
}

export interface DocumentWithDownloadUrlResponseDto extends DocumentResponseDto {
  downloadUrl: string;
  downloadUrlExpiresAt: number;
}

// Document Query DTO
export interface DocumentQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  documentType?: DocumentType;
  folderId?: number;
  isActive?: boolean;
  isPublic?: boolean;
  processingStatus?: ProcessingStatus;
  tags?: string;
  createdFrom?: number;
  createdTo?: number;
  minFileSize?: number;
  maxFileSize?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

// Document Search DTO
export interface DocumentSearchDto {
  searchText: string;
  page?: number;
  limit?: number;
  documentType?: DocumentType;
  folderId?: number;
}

// Create Document DTO
export interface CreateDocumentDto {
  title: string;
  description?: string;
  documentType: DocumentType;
  fileName: string;
  fileSize: number;
  mimeType: string;
  folderId?: number;
  s3Key: string;
  s3Bucket?: string;
  contentHash?: string;
  language?: string;
  isPublic?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

// Update Document DTO
export interface UpdateDocumentDto {
  title?: string;
  description?: string;
  documentType?: DocumentType;
  folderId?: number;
  isActive?: boolean;
  isPublic?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

// Upload Document DTO
export interface UploadDocumentDto {
  title: string;
  description?: string;
  documentType: DocumentType;
  folderId?: number;
  isPublic?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface UploadDocumentResponseDto {
  uploadUrl: string;
  uploadFields: Record<string, string>;
  documentId: number;
  expiresAt: number;
}

// Upload URL DTOs
export interface CreateUploadUrlDto {
  fileName: string;
  fileSize: number;
  mimeType: string;
  documentType: DocumentType;
  title?: string;
  description?: string;
  folderId?: number;
  isPublic?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface UploadUrlResponseDto {
  uploadUrl: string;
  uploadFields: Record<string, string>;
  documentId: number;
  expiresAt: number;
}

export interface ConfirmUploadDto {
  documentId: number;
  uploadSuccess: boolean;
  errorMessage?: string;
}

// Document Folder Types
export interface DocumentFolderResponseDto {
  id: number;
  name: string;
  description: string | null;
  parentId: number | null;
  path: string;
  level: number;
  isActive: boolean;
  documentCount: number;
  subfolderCount: number;
  createdAt: number;
  updatedAt: number | null;
  createdBy: number;
  updatedBy: number | null;
  tenantId: number;
}

export interface DocumentFolderQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  parentId?: number;
  isActive?: boolean;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

export interface CreateDocumentFolderDto {
  name: string;
  description?: string;
  parentId?: number;
}

export interface UpdateDocumentFolderDto {
  name?: string;
  description?: string;
  parentId?: number;
  isActive?: boolean;
}
