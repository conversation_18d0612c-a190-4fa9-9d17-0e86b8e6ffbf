import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, Card, Typography, IconButton, Tooltip } from '@/shared/components/common';
import { useTodoAttachmentQueue } from '@/shared/hooks/common/useTodoAttachmentQueue';

import { AttachmentType } from '../../types/attachment.types';

interface AttachmentUploaderProps {
  taskId: number;
  onSuccess: () => void;
}

interface UploadingFile {
  id: string;
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

/**
 * Component for uploading attachments
 */
const AttachmentUploader: React.FC<AttachmentUploaderProps> = ({ taskId, onSuccess }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);

  // Task queue for file uploads
  const attachmentQueue = useTodoAttachmentQueue({
    concurrency: 3,
    autoRemoveCompletedAfter: 30000, // 30 seconds
  });

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) {return;}

    // Create uploading file objects and start upload
    const newUploadingFiles: UploadingFile[] = Array.from(files).map(file => {
      const fileId = `${file.name}-${Date.now()}-${Math.random()}`;

      // Add to task queue for upload
      attachmentQueue.addTodoAttachmentUpload({
        todoId: taskId,
        file,
        onSuccess: (attachment) => {
          // Update file status to completed
          setUploadingFiles(prev =>
            prev.map(item =>
              item.id === fileId
                ? { ...item, status: 'success', progress: 100 }
                : item
            )
          );
          // Refresh attachments list
          onSuccess();
          // Remove from list after a delay
          setTimeout(() => {
            setUploadingFiles(prev => prev.filter(item => item.id !== fileId));
          }, 3000);
        },
        onError: (error) => {
          // Update file status to error
          setUploadingFiles(prev =>
            prev.map(item =>
              item.id === fileId
                ? {
                    ...item,
                    status: 'error',
                    error: error.message || t('todolist:attachment.notifications.uploadError', 'Error uploading file')
                  }
                : item
            )
          );
        },
        onProgress: (progress) => {
          // Update progress
          setUploadingFiles(prev =>
            prev.map(item =>
              item.id === fileId
                ? { ...item, progress }
                : item
            )
          );
        },
      });

      return {
        id: fileId,
        file,
        progress: 0,
        status: 'uploading' as const,
      };
    });

    setUploadingFiles(prev => [...prev, ...newUploadingFiles]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Determine attachment type from file
  const getAttachmentType = (file: File): AttachmentType => {
    const type = file.type.split('/')[0];
    switch (type) {
      case 'image':
        return AttachmentType.IMAGE;
      case 'video':
        return AttachmentType.VIDEO;
      case 'audio':
        return AttachmentType.AUDIO;
      default:
        if (
          file.type.includes('pdf') ||
          file.type.includes('document') ||
          file.type.includes('sheet') ||
          file.type.includes('presentation')
        ) {
          return AttachmentType.DOCUMENT;
        }
        return AttachmentType.OTHER;
    }
  };

  // Handle remove file
  const handleRemoveFile = (id: string) => {
    setUploadingFiles(prev => prev.filter(item => item.id !== id));
    // TODO: Cancel the upload task if it's still running
    // attachmentQueue.cancelTask(taskId);
  };

  // Handle click upload button
  const handleClickUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Typography variant="h6">{t('todolist:attachment.title', 'Attachments')}</Typography>
        <Button onClick={handleClickUpload} leftIcon="upload">
          {t('todolist:attachment.actions.upload', 'Upload File')}
        </Button>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileSelect}
          className="hidden"
          multiple
        />
      </div>

      {uploadingFiles.length > 0 && (
        <Card className="p-4">
          <Typography variant="subtitle2" className="mb-2">
            {t('todolist:attachment.uploading', 'Uploading Files')}
          </Typography>
          <div className="space-y-3">
            {uploadingFiles.map(file => (
              <div key={file.id} className="flex items-center space-x-3">
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <Typography variant="body2" className="font-medium truncate max-w-xs">
                      {file.file.name}
                    </Typography>
                    <Tooltip content={t('common:remove', 'Remove')}>
                      <IconButton
                        icon="x"
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveFile(file.id)}
                      />
                    </Tooltip>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full ${file.status === 'error' ? 'bg-red-500' : 'bg-blue-500'}`}
                      style={{ width: `${file.progress}%` }}
                    ></div>
                  </div>
                  {file.status === 'error' && (
                    <Typography variant="caption" className="text-red-500 mt-1">
                      {file.error}
                    </Typography>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default AttachmentUploader;
