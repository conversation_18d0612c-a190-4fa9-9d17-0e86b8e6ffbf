/**
 * Enum for Attachment Type
 */
export enum AttachmentType {
  IMAGE = 'IMAGE',
  DOCUMENT = 'DOCUMENT',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  OTHER = 'OTHER',
}

/**
 * Interface for Attachment
 */
export interface AttachmentDto {
  id: number;
  taskId: number;
  userId: number;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  thumbnailUrl?: string;
  type: AttachmentType;
  createdAt: number | null;
  updatedAt: number | null;
  userName?: string;
}

/**
 * Interface for Attachment Query
 */
export interface AttachmentQueryDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  taskId?: number;
  type?: AttachmentType;
}

/**
 * Interface for Create Attachment
 */
export interface CreateAttachmentDto {
  taskId: number;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  thumbnailUrl?: string;
  type: AttachmentType;
}

/**
 * Interface for Update Attachment
 */
export interface UpdateAttachmentDto {
  fileName?: string;
  thumbnailUrl?: string;
}

/**
 * DTO cho tạo presigned URL để upload tệp đính kèm
 */
export interface CreateUploadUrlDto {
  /**
   * ID của công việc
   */
  todoId: number;

  /**
   * Tên tệp đính kèm
   */
  fileName: string;

  /**
   * Loại nội dung của tệp (MIME type)
   */
  mimeType: string;

  /**
   * Kích thước tệp (byte)
   */
  fileSize: number;
}

/**
 * DTO cho response của presigned URL upload
 */
export interface UploadUrlResponseDto {
  /**
   * URL tạm thời để upload file
   */
  uploadUrl: string;

  /**
   * Key của file trên S3/Cloud Storage
   */
  s3Key: string;

  /**
   * ID để track upload
   */
  uploadId: string;

  /**
   * Thời gian hết hạn của URL (timestamp)
   */
  expiresAt: number;

  /**
   * Kích thước tối đa cho phép (byte)
   */
  maxFileSize: number;

  /**
   * MIME type được chấp nhận
   */
  acceptedMimeType: string;
}

/**
 * DTO cho xác nhận upload thành công
 */
export interface ConfirmUploadDto {
  /**
   * ID của công việc
   */
  todoId: number;

  /**
   * Key của file trên S3/Cloud Storage
   */
  s3Key: string;

  /**
   * Tên tệp đính kèm
   */
  fileName: string;

  /**
   * Loại nội dung của tệp (MIME type)
   */
  contentType?: string;

  /**
   * Kích thước tệp thực tế (byte)
   */
  size?: number;

  /**
   * ID upload để track (optional)
   */
  uploadId?: string;
}

/**
 * Response DTO cho todo attachment
 */
export interface TodoAttachmentResponseDto {
  id: number;
  todoId: number;
  filename: string;
  url: string;
  contentType?: string;
  size?: number;
  createdAt: number;
  createdBy: number;
}
