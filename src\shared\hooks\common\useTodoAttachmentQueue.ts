/**
 * Hook tích hợp useTaskQueue với todo attachment API
 */
import axios from 'axios';
import { useCallback } from 'react';

import { TodoAttachmentService } from '@/modules/todolist/services/attachment.service';
import { AttachmentType } from '@/modules/todolist/types/attachment.types';
import { NotificationUtil } from '@/shared/utils/notification';

import { useTaskQueue, UseTaskQueueOptions } from './useTaskQueue';

/**
 * Tham số cho upload todo attachment
 */
export interface TodoAttachmentUploadParams {
  /**
   * ID của todo
   */
  todoId: number;

  /**
   * File cần upload
   */
  file: File;

  /**
   * Callback khi upload thành công
   */
  onSuccess?: (attachment: any) => void;

  /**
   * Callback khi upload thất bại
   */
  onError?: (error: Error) => void;

  /**
   * Callback theo dõi tiến trình upload
   */
  onProgress?: (progress: number) => void;
}

/**
 * Xác định loại attachment từ file
 */
const getAttachmentType = (file: File): AttachmentType => {
  const type = file.type.split('/')[0];
  switch (type) {
    case 'image':
      return AttachmentType.IMAGE;
    case 'video':
      return AttachmentType.VIDEO;
    case 'audio':
      return AttachmentType.AUDIO;
    default:
      if (
        file.type.includes('pdf') ||
        file.type.includes('document') ||
        file.type.includes('sheet') ||
        file.type.includes('presentation')
      ) {
        return AttachmentType.DOCUMENT;
      }
      return AttachmentType.OTHER;
  }
};

/**
 * Hook tích hợp useTaskQueue với todo attachment API
 */
export function useTodoAttachmentQueue(options?: UseTaskQueueOptions) {
  const taskQueue = useTaskQueue(options);

  /**
   * Thêm task upload todo attachment vào queue
   */
  const addTodoAttachmentUpload = useCallback(
    (params: TodoAttachmentUploadParams): string => {
      const { todoId, file, onSuccess, onError, onProgress } = params;

      return taskQueue.addFileUploadTask({
        title: `Đang tải lên ${file.name}`,
        description: `Tải lên tệp đính kèm cho công việc #${todoId}`,
        file,
        
        // Hàm lấy upload URL
        getUploadUrl: async () => {
          try {
            const response = await TodoAttachmentService.createUploadUrl(
              todoId,
              file.name,
              file.type,
              file.size
            );
            return response.result.uploadUrl;
          } catch (error) {
            console.error('Lỗi khi lấy upload URL:', error);
            throw new Error('Không thể lấy URL upload');
          }
        },

        // Hàm thực thi upload
        execute: async (uploadUrl: string, uploadFile: File, progressCallback: (progress: number) => void) => {
          try {
            // Bước 1: Upload file lên S3/Cloud Storage
            await axios.put(uploadUrl, uploadFile, {
              headers: {
                'Content-Type': uploadFile.type,
              },
              onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / (progressEvent.total || 1)
                );
                progressCallback(percentCompleted);
              },
            });

            // Bước 2: Lấy thông tin upload từ URL để xác nhận
            const uploadUrlObj = new URL(uploadUrl);
            const s3Key = uploadUrlObj.pathname.substring(1); // Loại bỏ dấu '/' đầu tiên

            // Bước 3: Xác nhận upload với backend
            const confirmResponse = await TodoAttachmentService.confirmUpload(
              todoId,
              s3Key,
              uploadFile.name,
              uploadFile.type,
              uploadFile.size
            );

            return confirmResponse.result;
          } catch (error) {
            console.error('Lỗi khi upload file:', error);
            throw error;
          }
        },

        // Callbacks
        onSuccess: (result) => {
          NotificationUtil.success({
            message: `Đã tải lên ${file.name} thành công`,
          });
          if (onSuccess) {
            onSuccess(result);
          }
        },

        onError: (error) => {
          NotificationUtil.error({
            message: `Lỗi khi tải lên ${file.name}: ${error.message}`,
          });
          if (onError) {
            onError(error);
          }
        },

        onProgress,

        // Metadata
        metadata: {
          todoId,
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          attachmentType: getAttachmentType(file),
        },
      });
    },
    [taskQueue]
  );

  /**
   * Thêm nhiều file upload vào queue
   */
  const addMultipleTodoAttachmentUploads = useCallback(
    (
      todoId: number,
      files: File[],
      options?: {
        onSuccess?: (attachment: any) => void;
        onError?: (error: Error) => void;
        onProgress?: (progress: number) => void;
      }
    ): string[] => {
      return files.map(file =>
        addTodoAttachmentUpload({
          todoId,
          file,
          ...options,
        })
      );
    },
    [addTodoAttachmentUpload]
  );

  return {
    ...taskQueue,
    addTodoAttachmentUpload,
    addMultipleTodoAttachmentUploads,
  };
}

export default useTodoAttachmentQueue;
