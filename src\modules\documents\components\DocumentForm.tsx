import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Select,
  Typography,
} from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { useCreateDocument, useUpdateDocument } from '../hooks/useDocuments';
import {
  DocumentResponseDto,
  DocumentType,
  CreateDocumentDto,
  UpdateDocumentDto,
} from '../types/document.types';

interface DocumentFormProps {
  /**
   * Tài liệu cần cập nhật (nếu có)
   */
  document?: DocumentResponseDto;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi submit form thành công
   */
  onSuccess?: () => void;

  /**
   * Tiêu đề form
   */
  title?: string;
}

// Định nghĩa interface cho dữ liệu form
interface DocumentFormValues {
  title: string;
  description?: string;
  documentType: DocumentType;
  isPublic: boolean;
  tags?: string;
}

/**
 * Form tạo/cập nhật tài liệu
 */
const DocumentForm: React.FC<DocumentFormProps> = ({ 
  document, 
  onClose, 
  onSuccess, 
  title 
}) => {
  const { t } = useTranslation(['documents', 'common']);
  const isEditMode = !!document;

  // Mutations
  const createDocumentMutation = useCreateDocument();
  const updateDocumentMutation = useUpdateDocument();

  // Định nghĩa schema validation
  const documentSchema = z.object({
    title: z.string().min(1, t('common:validation.required', 'Trường này là bắt buộc')),
    description: z.string().optional(),
    documentType: z.nativeEnum(DocumentType),
    isPublic: z.boolean(),
    tags: z.string().optional(),
  });

  // Giá trị mặc định
  const defaultValues: DocumentFormValues = {
    title: document?.title || '',
    description: document?.description || '',
    documentType: document?.documentType || DocumentType.OTHER,
    isPublic: document?.isPublic || false,
    tags: document?.tags?.join(', ') || '',
  };

  // Options cho select document type
  const documentTypeOptions = [
    { value: DocumentType.POLICY, label: t('documents:type.policy', 'Chính sách') },
    { value: DocumentType.PROCEDURE, label: t('documents:type.procedure', 'Quy trình') },
    { value: DocumentType.MANUAL, label: t('documents:type.manual', 'Hướng dẫn') },
    { value: DocumentType.FORM, label: t('documents:type.form', 'Biểu mẫu') },
    { value: DocumentType.TEMPLATE, label: t('documents:type.template', 'Mẫu') },
    { value: DocumentType.REPORT, label: t('documents:type.report', 'Báo cáo') },
    { value: DocumentType.CONTRACT, label: t('documents:type.contract', 'Hợp đồng') },
    { value: DocumentType.PRESENTATION, label: t('documents:type.presentation', 'Thuyết trình') },
    { value: DocumentType.SPREADSHEET, label: t('documents:type.spreadsheet', 'Bảng tính') },
    { value: DocumentType.IMAGE, label: t('documents:type.image', 'Hình ảnh') },
    { value: DocumentType.VIDEO, label: t('documents:type.video', 'Video') },
    { value: DocumentType.AUDIO, label: t('documents:type.audio', 'Âm thanh') },
    { value: DocumentType.OTHER, label: t('documents:type.other', 'Khác') },
  ];

  // Options cho select isPublic
  const isPublicOptions = [
    { value: false, label: t('documents:privacy.private', 'Riêng tư') },
    { value: true, label: t('documents:privacy.public', 'Công khai') },
  ];

  // Xử lý submit form
  const handleSubmit = async (values: DocumentFormValues) => {
    try {
      const tagsArray = values.tags 
        ? values.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
        : [];

      if (isEditMode && document) {
        // Cập nhật tài liệu
        const updateData: UpdateDocumentDto = {
          title: values.title,
          description: values.description || null,
          documentType: values.documentType,
          isPublic: values.isPublic,
          tags: tagsArray,
        };

        await updateDocumentMutation.mutateAsync({ id: document.id, data: updateData });
        NotificationUtil.success({
          message: t('documents:messages.updateSuccess', 'Cập nhật tài liệu thành công'),
        });
      } else {
        // Tạo tài liệu mới - Note: Trong thực tế cần upload file trước
        NotificationUtil.info({
          message: t('documents:messages.uploadRequired', 'Vui lòng sử dụng chức năng upload để tạo tài liệu mới'),
        });
        return;
      }

      // Gọi callback thành công và đóng form
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
      NotificationUtil.error({
        message: isEditMode
          ? t('documents:messages.updateError', 'Cập nhật tài liệu thất bại')
          : t('documents:messages.createError', 'Tạo tài liệu thất bại'),
      });
    }
  };

  return (
    <Card className="p-4" allowOverflow={true}>
      <Typography variant="h5" className="mb-4">
        {title ||
          (isEditMode
            ? t('documents:form.editTitle', 'Cập nhật tài liệu')
            : t('documents:form.title', 'Thêm tài liệu mới'))}
      </Typography>

      <Form
        schema={documentSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        className="space-y-4"
      >
        {/* Tiêu đề */}
        <FormItem name="title" label={t('documents:form.title', 'Tiêu đề')} required>
          <Input
            placeholder={t('documents:form.titlePlaceholder', 'Nhập tiêu đề tài liệu')}
            fullWidth
          />
        </FormItem>

        {/* Mô tả */}
        <FormItem name="description" label={t('documents:form.description', 'Mô tả')}>
          <Input
            placeholder={t('documents:form.descriptionPlaceholder', 'Nhập mô tả tài liệu')}
            fullWidth
          />
        </FormItem>

        {/* Loại tài liệu */}
        <FormItem name="documentType" label={t('documents:form.type', 'Loại tài liệu')} required>
          <Select
            options={documentTypeOptions}
            placeholder={t('documents:form.typePlaceholder', 'Chọn loại tài liệu')}
            fullWidth
          />
        </FormItem>

        {/* Quyền riêng tư */}
        <FormItem name="isPublic" label={t('documents:form.privacy', 'Quyền truy cập')}>
          <Select
            options={isPublicOptions}
            placeholder={t('documents:form.privacyPlaceholder', 'Chọn quyền truy cập')}
            fullWidth
          />
        </FormItem>

        {/* Tags */}
        <FormItem name="tags" label={t('documents:form.tags', 'Thẻ tags')}>
          <Input
            placeholder={t('documents:form.tagsPlaceholder', 'Nhập các thẻ, cách nhau bằng dấu phẩy')}
            fullWidth
          />
        </FormItem>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={createDocumentMutation.isPending || updateDocumentMutation.isPending}
          >
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={createDocumentMutation.isPending || updateDocumentMutation.isPending}
          >
            {isEditMode ? t('common:update', 'Cập nhật') : t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default DocumentForm;
