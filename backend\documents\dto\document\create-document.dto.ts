import {
  IsString,
  IsOptional,
  IsNumber,
  IsEnum,
  IsArray,
  IsObject,
  MaxLength,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentType } from '../../enums';

/**
 * DTO để tạo tài liệu mới
 */
export class CreateDocumentDto {
  /**
   * Tiêu đề tài liệu
   */
  @ApiProperty({
    description: 'Tiêu đề tài liệu',
    example: '<PERSON><PERSON>h sách làm việc từ xa',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  title: string;

  /**
   * Mô tả tài liệu
   */
  @ApiPropertyOptional({
    description: 'Mô tả tài liệu',
    example: 'Quy định về làm việc từ xa cho nhân viên công ty',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * <PERSON>ại tài liệu
   */
  @ApiProperty({
    description: 'Loại tài liệu',
    enum: DocumentType,
    example: DocumentType.POLICY,
  })
  @IsEnum(DocumentType)
  documentType: DocumentType;

  /**
   * Tên file gốc
   */
  @ApiProperty({
    description: 'Tên file gốc',
    example: 'chinh-sach-lam-viec-tu-xa.pdf',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  fileName: string;

  /**
   * Kích thước file (bytes)
   */
  @ApiProperty({
    description: 'Kích thước file tính bằng bytes',
    example: 1048576,
  })
  @IsNumber()
  fileSize: number;

  /**
   * Loại MIME
   */
  @ApiProperty({
    description: 'Loại MIME của file',
    example: 'application/pdf',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  mimeType: string;

  /**
   * ID thư mục
   */
  @ApiPropertyOptional({
    description: 'ID thư mục chứa tài liệu',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  folderId?: number;

  /**
   * Khóa object S3
   */
  @ApiProperty({
    description: 'Khóa object S3',
    example: 'documents/tenant-1/2024/01/chinh-sach-lam-viec-tu-xa.pdf',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  s3Key: string;

  /**
   * Tên bucket S3
   */
  @ApiPropertyOptional({
    description: 'Tên bucket S3',
    example: 'company-documents',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  s3Bucket?: string;

  /**
   * Vùng S3
   */
  @ApiPropertyOptional({
    description: 'Vùng S3',
    example: 'ap-southeast-1',
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  s3Region?: string;

  /**
   * ETag S3
   */
  @ApiPropertyOptional({
    description: 'ETag S3',
    example: 'd41d8cd98f00b204e9800998ecf8427e',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  s3Etag?: string;

  /**
   * ID phiên bản S3
   */
  @ApiPropertyOptional({
    description: 'ID phiên bản S3',
    example: 'null',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  s3VersionId?: string;

  /**
   * Hash nội dung SHA-256
   */
  @ApiPropertyOptional({
    description: 'Hash SHA-256 của nội dung file',
    example: 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
    maxLength: 64,
  })
  @IsOptional()
  @IsString()
  @MaxLength(64)
  contentHash?: string;

  /**
   * Số trang
   */
  @ApiPropertyOptional({
    description: 'Số trang (cho tài liệu PDF)',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  pageCount?: number;

  /**
   * Số từ
   */
  @ApiPropertyOptional({
    description: 'Số từ trong tài liệu',
    example: 1500,
  })
  @IsOptional()
  @IsNumber()
  wordCount?: number;

  /**
   * Ngôn ngữ
   */
  @ApiPropertyOptional({
    description: 'Ngôn ngữ của tài liệu',
    example: 'vi',
    default: 'vi',
    maxLength: 10,
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  language?: string = 'vi';

  /**
   * Nội dung văn bản đã trích xuất
   */
  @ApiPropertyOptional({
    description: 'Nội dung văn bản đã trích xuất để tìm kiếm',
  })
  @IsOptional()
  @IsString()
  extractedText?: string;

  /**
   * Tài liệu có công khai không
   */
  @ApiPropertyOptional({
    description: 'Tài liệu có công khai không (bỏ qua kiểm tra quyền)',
    example: false,
    default: false,
  })
  @IsOptional()
  isPublic?: boolean = false;

  /**
   * Thẻ tag
   */
  @ApiPropertyOptional({
    description: 'Danh sách thẻ tag',
    example: ['hr', 'policy', 'remote-work'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung của tài liệu',
    example: {
      author: 'HR Department',
      version: '1.0',
      effectiveDate: '2024-01-01',
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
