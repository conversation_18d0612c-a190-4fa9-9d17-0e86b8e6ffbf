import { Injectable, Logger } from '@nestjs/common';
import { DocumentPermissionRepository } from '../repositories/document-permission.repository';
import { DocumentRepository } from '../repositories/document.repository';
import { CreateDocumentPermissionDto } from '../dto/document-permission';
import { DocumentPermission } from '../entities/document-permission.entity';
import { PermissionLevel, hasPermission } from '../enums';
import { AppException } from '@/common';
import { DOCUMENTS_ERROR_CODES } from '../errors';

/**
 * Service xử lý logic nghiệp vụ cho phân quyền tài liệu
 */
@Injectable()
export class DocumentPermissionService {
  private readonly logger = new Logger(DocumentPermissionService.name);

  constructor(
    private readonly documentPermissionRepository: DocumentPermissionRepository,
    private readonly documentRepository: DocumentRepository,
  ) {}

  /**
   * Tạo quyền tài liệu mới
   * @param tenantId ID tenant
   * @param createDto Dữ liệu tạo quyền
   * @param grantedBy ID người cấp quyền
   * @returns Quyền đã tạo
   */
  async create(
    tenantId: number,
    createDto: CreateDocumentPermissionDto,
    grantedBy: number,
  ): Promise<DocumentPermission> {
    this.logger.log(
      `Tạo quyền tài liệu cho document ${createDto.documentId} - tenant ${tenantId}`,
    );

    // Kiểm tra tài liệu tồn tại
    const document = await this.documentRepository.findById(
      tenantId,
      createDto.documentId,
    );
    if (!document) {
      throw new AppException(DOCUMENTS_ERROR_CODES.DOCUMENT_NOT_FOUND);
    }

    // Kiểm tra chỉ có một target được set
    const targetCount = [
      createDto.userId,
      createDto.roleId,
      createDto.departmentId,
    ].filter(Boolean).length;
    if (targetCount !== 1) {
      throw new AppException(DOCUMENTS_ERROR_CODES.INVALID_PERMISSION_TARGET);
    }

    // Kiểm tra quyền đã tồn tại chưa
    let existingPermission: DocumentPermission | null = null;

    if (createDto.userId) {
      existingPermission =
        await this.documentPermissionRepository.findUserPermission(
          tenantId,
          createDto.documentId,
          createDto.userId,
        );
    } else if (createDto.roleId) {
      existingPermission =
        await this.documentPermissionRepository.findRolePermission(
          tenantId,
          createDto.documentId,
          createDto.roleId,
        );
    } else if (createDto.departmentId) {
      existingPermission =
        await this.documentPermissionRepository.findDepartmentPermission(
          tenantId,
          createDto.documentId,
          createDto.departmentId,
        );
    }

    if (existingPermission) {
      throw new AppException(DOCUMENTS_ERROR_CODES.PERMISSION_ALREADY_EXISTS);
    }

    // Tạo quyền mới
    const permissionData: Partial<DocumentPermission> = {
      ...createDto,
      grantedBy,
      grantedAt: Date.now(),
    };

    const permission = await this.documentPermissionRepository.create(
      tenantId,
      permissionData,
    );

    this.logger.log(`Đã tạo quyền ID: ${permission.id}`);
    return permission;
  }

  /**
   * Kiểm tra quyền của user đối với tài liệu
   * @param tenantId ID tenant
   * @param documentId ID tài liệu
   * @param userId ID user
   * @param requiredLevel Cấp độ quyền yêu cầu
   * @returns True nếu có quyền
   */
  async checkUserPermission(
    tenantId: number,
    documentId: number,
    userId: number,
    requiredLevel: PermissionLevel,
  ): Promise<boolean> {
    this.logger.log(
      `Kiểm tra quyền user ${userId} cho document ${documentId} - level ${requiredLevel}`,
    );

    // Kiểm tra tài liệu có công khai không
    const document = await this.documentRepository.findById(
      tenantId,
      documentId,
    );
    if (!document) {
      return false;
    }

    if (document.isPublic) {
      this.logger.log('Tài liệu công khai - cho phép truy cập');
      return true;
    }

    // Kiểm tra quyền trực tiếp của user
    const userPermission =
      await this.documentPermissionRepository.findUserPermission(
        tenantId,
        documentId,
        userId,
      );

    if (userPermission && userPermission.isValid) {
      const hasRequiredPermission = hasPermission(
        userPermission.permissionLevel,
        requiredLevel,
      );
      if (hasRequiredPermission) {
        this.logger.log(
          `User có quyền trực tiếp: ${userPermission.permissionLevel}`,
        );
        return true;
      }
    }

    // TODO: Kiểm tra quyền thông qua role
    // const userRoles = await this.userService.getUserRoles(userId);
    // for (const role of userRoles) {
    //   const rolePermission = await this.documentPermissionRepository.findRolePermission(
    //     tenantId,
    //     documentId,
    //     role.id
    //   );
    //   if (rolePermission && rolePermission.isValid) {
    //     const hasRequiredPermission = hasPermission(rolePermission.permissionLevel, requiredLevel);
    //     if (hasRequiredPermission) {
    //       this.logger.log(`User có quyền thông qua role: ${rolePermission.permissionLevel}`);
    //       return true;
    //     }
    //   }
    // }

    // TODO: Kiểm tra quyền thông qua department
    // const userDepartment = await this.userService.getUserDepartment(userId);
    // if (userDepartment) {
    //   const deptPermission = await this.documentPermissionRepository.findDepartmentPermission(
    //     tenantId,
    //     documentId,
    //     userDepartment.id
    //   );
    //   if (deptPermission && deptPermission.isValid) {
    //     const hasRequiredPermission = hasPermission(deptPermission.permissionLevel, requiredLevel);
    //     if (hasRequiredPermission) {
    //       this.logger.log(`User có quyền thông qua department: ${deptPermission.permissionLevel}`);
    //       return true;
    //     }
    //   }
    // }

    this.logger.log('User không có quyền truy cập');
    return false;
  }

  /**
   * Lấy tất cả quyền của tài liệu
   * @param tenantId ID tenant
   * @param documentId ID tài liệu
   * @returns Danh sách quyền
   */
  async getDocumentPermissions(
    tenantId: number,
    documentId: number,
  ): Promise<DocumentPermission[]> {
    this.logger.log(
      `Lấy danh sách quyền cho document ${documentId} - tenant ${tenantId}`,
    );

    // Kiểm tra tài liệu tồn tại
    const document = await this.documentRepository.findById(
      tenantId,
      documentId,
    );
    if (!document) {
      throw new AppException(DOCUMENTS_ERROR_CODES.DOCUMENT_NOT_FOUND);
    }

    return this.documentPermissionRepository.findByDocument(
      tenantId,
      documentId,
    );
  }

  /**
   * Cập nhật quyền
   * @param tenantId ID tenant
   * @param permissionId ID quyền
   * @param updateData Dữ liệu cập nhật
   * @param updatedBy ID người cập nhật
   * @returns Quyền đã cập nhật hoặc null
   */
  async update(
    tenantId: number,
    permissionId: number,
    updateData: Partial<DocumentPermission>,
    updatedBy: number,
  ): Promise<DocumentPermission | null> {
    this.logger.log(
      `Cập nhật quyền ID: ${permissionId} cho tenant ${tenantId}`,
    );

    // Kiểm tra quyền tồn tại
    const existingPermission = await this.documentPermissionRepository.findById(
      tenantId,
      permissionId,
    );
    if (!existingPermission) {
      return null;
    }

    const updatedPermission = await this.documentPermissionRepository.update(
      tenantId,
      permissionId,
      {
        ...updateData,
        // Không cho phép thay đổi một số field quan trọng
        documentId: existingPermission.documentId,
        userId: existingPermission.userId,
        roleId: existingPermission.roleId,
        departmentId: existingPermission.departmentId,
        grantedBy: existingPermission.grantedBy,
        grantedAt: existingPermission.grantedAt,
      },
    );

    if (updatedPermission) {
      this.logger.log(`Đã cập nhật quyền ID: ${permissionId}`);
    }

    return updatedPermission;
  }

  /**
   * Xóa quyền
   * @param tenantId ID tenant
   * @param permissionId ID quyền
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number, permissionId: number): Promise<boolean> {
    this.logger.log(`Xóa quyền ID: ${permissionId} cho tenant ${tenantId}`);

    const success = await this.documentPermissionRepository.delete(
      tenantId,
      permissionId,
    );

    if (success) {
      this.logger.log(`Đã xóa quyền ID: ${permissionId}`);
    }

    return success;
  }

  /**
   * Xóa tất cả quyền của tài liệu
   * @param tenantId ID tenant
   * @param documentId ID tài liệu
   * @returns Số quyền đã xóa
   */
  async deleteAllDocumentPermissions(
    tenantId: number,
    documentId: number,
  ): Promise<number> {
    this.logger.log(
      `Xóa tất cả quyền cho document ${documentId} - tenant ${tenantId}`,
    );

    const deletedCount =
      await this.documentPermissionRepository.deleteByDocument(
        tenantId,
        documentId,
      );

    this.logger.log(`Đã xóa ${deletedCount} quyền cho document ${documentId}`);
    return deletedCount;
  }

  /**
   * Dọn dẹp quyền hết hạn
   * @param tenantId ID tenant
   * @returns Số quyền đã xóa
   */
  async cleanupExpiredPermissions(tenantId: number): Promise<number> {
    this.logger.log(`Dọn dẹp quyền hết hạn cho tenant ${tenantId}`);

    const deletedCount =
      await this.documentPermissionRepository.deleteExpired(tenantId);

    this.logger.log(
      `Đã xóa ${deletedCount} quyền hết hạn cho tenant ${tenantId}`,
    );
    return deletedCount;
  }

  /**
   * Lấy quyền sắp hết hạn
   * @param tenantId ID tenant
   * @param daysBeforeExpiration Số ngày trước khi hết hạn
   * @returns Danh sách quyền sắp hết hạn
   */
  async getExpiringSoonPermissions(
    tenantId: number,
    daysBeforeExpiration: number = 7,
  ): Promise<DocumentPermission[]> {
    this.logger.log(
      `Lấy quyền sắp hết hạn trong ${daysBeforeExpiration} ngày cho tenant ${tenantId}`,
    );

    return this.documentPermissionRepository.findExpiringSoon(
      tenantId,
      daysBeforeExpiration,
    );
  }

  /**
   * Lấy thống kê quyền theo cấp độ
   * @param tenantId ID tenant
   * @param documentId ID tài liệu (tùy chọn)
   * @returns Thống kê theo cấp độ quyền
   */
  async getPermissionStats(
    tenantId: number,
    documentId?: number,
  ): Promise<Record<PermissionLevel, number>> {
    this.logger.log(
      `Lấy thống kê quyền cho tenant ${tenantId}${documentId ? ` - document ${documentId}` : ''}`,
    );

    return this.documentPermissionRepository.countByLevel(tenantId, documentId);
  }
}
