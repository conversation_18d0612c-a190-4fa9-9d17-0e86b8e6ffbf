import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsEnum,
  IsBoolean,
  MaxLength,
} from 'class-validator';
import { DocumentType } from '../../enums';

/**
 * DTO để tạo presigned URL cho upload tài liệu
 */
export class CreateUploadUrlDto {
  /**
   * Tên file
   */
  @ApiProperty({
    description: 'Tên file cần upload',
    example: 'chinh-sach-lam-viec-tu-xa.pdf',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  fileName: string;

  /**
   * Loại MIME
   */
  @ApiProperty({
    description: 'Loại MIME của file',
    example: 'application/pdf',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  mimeType: string;

  /**
   * <PERSON><PERSON><PERSON> thước file (bytes)
   */
  @ApiProperty({
    description: '<PERSON><PERSON>ch thước file tính bằng bytes',
    example: 1048576,
  })
  @IsNumber()
  fileSize: number;

  /**
   * Tiêu đề tài liệu
   */
  @ApiProperty({
    description: 'Tiêu đề tài liệu',
    example: 'Chính sách làm việc từ xa',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  title: string;

  /**
   * Mô tả tài liệu
   */
  @ApiPropertyOptional({
    description: 'Mô tả tài liệu',
    example: 'Quy định về làm việc từ xa cho nhân viên công ty',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Loại tài liệu
   */
  @ApiProperty({
    description: 'Loại tài liệu',
    enum: DocumentType,
    example: DocumentType.POLICY,
  })
  @IsEnum(DocumentType)
  documentType: DocumentType;

  /**
   * ID thư mục
   */
  @ApiPropertyOptional({
    description: 'ID thư mục chứa tài liệu',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  folderId?: number;

  /**
   * Có phải tài liệu công khai
   */
  @ApiPropertyOptional({
    description: 'Có phải tài liệu công khai',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  /**
   * Tags (JSON string)
   */
  @ApiPropertyOptional({
    description: 'Tags của tài liệu (JSON string)',
    example: '["policy", "remote-work", "hr"]',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  /**
   * Metadata bổ sung (JSON string)
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung của tài liệu (JSON string)',
    example: '{"author": "HR Department", "version": "1.0"}',
  })
  @IsOptional()
  @IsString()
  metadata?: string;
}

/**
 * Response DTO chứa presigned URL và metadata
 */
export class UploadUrlResponseDto {
  /**
   * Presigned URL để upload
   */
  @ApiProperty({
    description: 'Presigned URL để upload file',
    example: 'https://s3.amazonaws.com/bucket/documents/tenant-1/file.pdf?signature=...',
  })
  uploadUrl: string;

  /**
   * S3 key của file
   */
  @ApiProperty({
    description: 'S3 key của file',
    example: 'documents/tenant-1/2024/01/chinh-sach-lam-viec-tu-xa.pdf',
  })
  s3Key: string;

  /**
   * ID tạm thời để confirm upload
   */
  @ApiProperty({
    description: 'ID tạm thời để confirm upload',
    example: 'upload_123456789',
  })
  uploadId: string;

  /**
   * Thời gian hết hạn của URL (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian hết hạn của presigned URL (timestamp)',
    example: 1640995200000,
  })
  expiresAt: number;

  /**
   * Kích thước tối đa cho phép (bytes)
   */
  @ApiProperty({
    description: 'Kích thước tối đa cho phép (bytes)',
    example: 52428800,
  })
  maxFileSize: number;

  /**
   * Loại MIME được chấp nhận
   */
  @ApiProperty({
    description: 'Loại MIME được chấp nhận',
    example: 'application/pdf',
  })
  acceptedMimeType: string;
}

/**
 * DTO để confirm upload thành công
 */
export class ConfirmUploadDto {
  /**
   * ID upload từ response của create upload URL
   */
  @ApiProperty({
    description: 'ID upload từ response của create upload URL',
    example: 'upload_123456789',
  })
  @IsString()
  @IsNotEmpty()
  uploadId: string;

  /**
   * S3 key của file đã upload
   */
  @ApiProperty({
    description: 'S3 key của file đã upload',
    example: 'documents/tenant-1/2024/01/chinh-sach-lam-viec-tu-xa.pdf',
  })
  @IsString()
  @IsNotEmpty()
  s3Key: string;

  /**
   * ETag từ S3 response (optional)
   */
  @ApiPropertyOptional({
    description: 'ETag từ S3 response sau khi upload',
    example: '"d41d8cd98f00b204e9800998ecf8427e"',
  })
  @IsOptional()
  @IsString()
  etag?: string;
}
