import {
  DocumentQueryDto,
  DocumentSearchDto,
  DocumentFolderQueryDto,
} from './types/document.types';

/**
 * Query keys cho Documents module
 */

// Document Query Keys
export const DOCUMENT_QUERY_KEYS = {
  ALL: ['documents'] as const,
  LIST: (params: DocumentQueryDto) => [...DOCUMENT_QUERY_KEYS.ALL, 'list', params] as const,
  SEARCH: (params: DocumentSearchDto) => [...DOCUMENT_QUERY_KEYS.ALL, 'search', params] as const,
  DETAIL: (id: number) => [...DOCUMENT_QUERY_KEYS.ALL, 'detail', id] as const,
  DOWNLOAD_URL: (id: number) => [...DOCUMENT_QUERY_KEYS.ALL, 'download-url', id] as const,
  STATS: () => [...DOCUMENT_QUERY_KEYS.ALL, 'stats'] as const,
} as const;

// Document Folder Query Keys
export const DOCUMENT_FOLDER_QUERY_KEYS = {
  ALL: ['document-folders'] as const,
  LIST: (params: DocumentFolderQueryDto) => [...DOCUMENT_FOLDER_QUERY_KEYS.ALL, 'list', params] as const,
  TREE: () => [...DOCUMENT_FOLDER_QUERY_KEYS.ALL, 'tree'] as const,
  DETAIL: (id: number) => [...DOCUMENT_FOLDER_QUERY_KEYS.ALL, 'detail', id] as const,
  PATH: (id: number) => [...DOCUMENT_FOLDER_QUERY_KEYS.ALL, 'path', id] as const,
  CHILDREN: (id: number, params: DocumentFolderQueryDto) => 
    [...DOCUMENT_FOLDER_QUERY_KEYS.ALL, 'children', id, params] as const,
} as const;

/**
 * Default values cho Documents module
 */
export const DOCUMENT_DEFAULTS = {
  PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  ALLOWED_MIME_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/gif',
  ],
  SEARCH_MIN_LENGTH: 2,
  BULK_DELETE_LIMIT: 50,
} as const;

export const DOCUMENT_FOLDER_DEFAULTS = {
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  MAX_NAME_LENGTH: 255,
  BULK_DELETE_LIMIT: 20,
} as const;
