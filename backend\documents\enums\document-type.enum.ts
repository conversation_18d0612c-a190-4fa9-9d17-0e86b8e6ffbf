/**
 * Enum for document types in the document management system
 */
export enum DocumentType {
  POLICY = 'policy',
  PROCEDURE = 'procedure',
  GUIDELINE = 'guideline',
  MANUAL = 'manual',
  FORM = 'form',
  TEMPLATE = 'template',
  REPORT = 'report',
  OTHER = 'other',
}

/**
 * Human-readable labels for document types
 */
export const DocumentTypeLabels = {
  [DocumentType.POLICY]: 'Chính sách',
  [DocumentType.PROCEDURE]: 'Quy trình',
  [DocumentType.GUIDELINE]: 'Hướng dẫn',
  [DocumentType.MANUAL]: 'Sổ tay',
  [DocumentType.FORM]: 'Biểu mẫu',
  [DocumentType.TEMPLATE]: 'Mẫu',
  [DocumentType.REPORT]: 'Báo cáo',
  [DocumentType.OTHER]: 'Khác',
};
