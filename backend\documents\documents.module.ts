import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  DocumentFolder,
  OpenAIVectorStore,
  Document,
  DocumentPermission,
  DocumentAccessLog,
} from './entities';

// Repositories
import {
  DocumentFolderRepository,
  OpenAIVectorStoreRepository,
  DocumentRepository,
  DocumentPermissionRepository,
  DocumentAccessLogRepository,
} from './repositories';

// Services
import {
  DocumentFolderService,
  DocumentService,
  DocumentPermissionService,
} from './services';
import { S3Service } from '@/shared/services/s3.service';

// Controllers
import {
  DocumentController,
  DocumentFolderController,
} from './controllers';

/**
 * Module quản lý tài liệu với OpenAI integration và tenant isolation
 */
@Global()
@Module({
  imports: [
    // Import TypeORM entities
    TypeOrmModule.forFeature([
      DocumentFolder,
      OpenAIVectorStore,
      Document,
      DocumentPermission,
      DocumentAccessLog,
    ]),
  ],
  controllers: [
    DocumentController,
    DocumentFolderController,
  ],
  providers: [
    // Repositories
    DocumentFolderRepository,
    OpenAIVectorStoreRepository,
    DocumentRepository,
    DocumentPermissionRepository,
    DocumentAccessLogRepository,

    // Services
    S3Service,
    DocumentFolderService,
    DocumentService,
    DocumentPermissionService,
  ],
  exports: [
    // Export repositories để các module khác có thể sử dụng
    DocumentFolderRepository,
    OpenAIVectorStoreRepository,
    DocumentRepository,
    DocumentPermissionRepository,
    DocumentAccessLogRepository,

    // Export services để các module khác có thể sử dụng
    DocumentFolderService,
    DocumentService,
    DocumentPermissionService,
  ],
})
export class DocumentsModule {}

/**
 * Module chỉ export entities để các module khác import
 * Sử dụng khi chỉ cần entities mà không cần services
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      DocumentFolder,
      OpenAIVectorStore,
      Document,
      DocumentPermission,
      DocumentAccessLog,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DocumentsEntitiesModule {}
