import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  DocumentFolderResponseDto,
  DocumentFolderQueryDto,
  CreateDocumentFolderDto,
  UpdateDocumentFolderDto,
} from '../types/document.types';
import * as DocumentFolderService from '../services/document-folder.service';
import { DOCUMENT_FOLDER_QUERY_KEYS } from '../constants';

/**
 * Hook để lấy danh sách thư mục
 */
export const useDocumentFolders = (params?: DocumentFolderQueryDto) => {
  return useQuery({
    queryKey: DOCUMENT_FOLDER_QUERY_KEYS.LIST(params || {}),
    queryFn: () => DocumentFolderService.getDocumentFoldersWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy cây thư mục
 */
export const useDocumentFolderTree = () => {
  return useQuery({
    queryKey: DOCUMENT_FOLDER_QUERY_KEYS.TREE(),
    queryFn: () => DocumentFolderService.getDocumentFolderTreeWithBusinessLogic(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy chi tiết thư mục
 */
export const useDocumentFolder = (id: number) => {
  return useQuery({
    queryKey: DOCUMENT_FOLDER_QUERY_KEYS.DETAIL(id),
    queryFn: () => DocumentFolderService.getDocumentFolderByIdWithBusinessLogic(id),
    enabled: !!id && id > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy đường dẫn thư mục
 */
export const useDocumentFolderPath = (id: number) => {
  return useQuery({
    queryKey: DOCUMENT_FOLDER_QUERY_KEYS.PATH(id),
    queryFn: () => DocumentFolderService.getDocumentFolderPathWithBusinessLogic(id),
    enabled: !!id && id > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy thư mục con
 */
export const useDocumentFolderChildren = (id: number, params?: DocumentFolderQueryDto) => {
  return useQuery({
    queryKey: DOCUMENT_FOLDER_QUERY_KEYS.CHILDREN(id, params || {}),
    queryFn: () => DocumentFolderService.getDocumentFolderChildrenWithBusinessLogic(id, params),
    enabled: !!id && id > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để tạo thư mục mới
 */
export const useCreateDocumentFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateDocumentFolderDto) =>
      DocumentFolderService.createDocumentFolderWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate and refetch folders list and tree
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_FOLDER_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để cập nhật thư mục
 */
export const useUpdateDocumentFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateDocumentFolderDto }) =>
      DocumentFolderService.updateDocumentFolderWithBusinessLogic(id, data),
    onSuccess: (updatedFolder) => {
      // Update specific folder in cache
      queryClient.setQueryData(
        DOCUMENT_FOLDER_QUERY_KEYS.DETAIL(updatedFolder.id),
        updatedFolder
      );

      // Invalidate folders list and tree
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_FOLDER_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để xóa thư mục
 */
export const useDeleteDocumentFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => DocumentFolderService.deleteDocumentFolderWithBusinessLogic(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: DOCUMENT_FOLDER_QUERY_KEYS.DETAIL(deletedId),
      });

      // Invalidate folders list and tree
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_FOLDER_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để xóa nhiều thư mục
 */
export const useBulkDeleteDocumentFolders = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) =>
      DocumentFolderService.bulkDeleteDocumentFoldersWithBusinessLogic(ids),
    onSuccess: (_, deletedIds) => {
      // Remove from cache
      deletedIds.forEach(id => {
        queryClient.removeQueries({
          queryKey: DOCUMENT_FOLDER_QUERY_KEYS.DETAIL(id),
        });
      });

      // Invalidate folders list and tree
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_FOLDER_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để di chuyển thư mục
 */
export const useMoveDocumentFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, parentId }: { id: number; parentId: number | null }) =>
      DocumentFolderService.moveDocumentFolderWithBusinessLogic(id, parentId),
    onSuccess: (movedFolder) => {
      // Update specific folder in cache
      queryClient.setQueryData(
        DOCUMENT_FOLDER_QUERY_KEYS.DETAIL(movedFolder.id),
        movedFolder
      );

      // Invalidate folders list and tree
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_FOLDER_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để sao chép thư mục
 */
export const useDuplicateDocumentFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      id, 
      name, 
      parentId 
    }: { 
      id: number; 
      name?: string; 
      parentId?: number | null 
    }) =>
      DocumentFolderService.duplicateDocumentFolderWithBusinessLogic(id, name, parentId),
    onSuccess: () => {
      // Invalidate folders list and tree
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_FOLDER_QUERY_KEYS.ALL,
      });
    },
  });
};
