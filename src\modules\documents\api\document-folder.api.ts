import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  DocumentFolderResponseDto,
  DocumentFolderQueryDto,
  CreateDocumentFolderDto,
  UpdateDocumentFolderDto,
} from '../types/document.types';

/**
 * Document Folder API Layer
 * Raw API calls without business logic
 */

/**
 * T<PERSON>o thư mục mới
 */
export const createDocumentFolder = async (
  data: CreateDocumentFolderDto
): Promise<ApiResponseDto<DocumentFolderResponseDto>> => {
  return apiClient.post('/v1/api/document-folders', data);
};

/**
 * L<PERSON>y danh sách thư mục
 */
export const getDocumentFolders = async (
  params?: DocumentFolderQueryDto
): Promise<ApiResponseDto<PaginatedResult<DocumentFolderResponseDto>>> => {
  return apiClient.get('/v1/api/document-folders', { params });
};

/**
 * Lấy cây thư mục
 */
export const getDocumentFolderTree = async (): Promise<
  ApiResponseDto<DocumentFolderResponseDto[]>
> => {
  return apiClient.get('/v1/api/document-folders/tree');
};

/**
 * Lấy chi tiết thư mục
 */
export const getDocumentFolderById = async (
  id: number
): Promise<ApiResponseDto<DocumentFolderResponseDto | null>> => {
  return apiClient.get(`/v1/api/document-folders/${id}`);
};

/**
 * Cập nhật thư mục
 */
export const updateDocumentFolder = async (
  id: number,
  data: UpdateDocumentFolderDto
): Promise<ApiResponseDto<DocumentFolderResponseDto>> => {
  return apiClient.put(`/v1/api/document-folders/${id}`, data);
};

/**
 * Xóa thư mục
 */
export const deleteDocumentFolder = async (
  id: number
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/v1/api/document-folders/${id}`);
};

/**
 * Xóa nhiều thư mục
 */
export const bulkDeleteDocumentFolders = async (
  ids: number[]
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete('/v1/api/document-folders/bulk-delete', {
    data: { ids },
  });
};

/**
 * Di chuyển thư mục
 */
export const moveDocumentFolder = async (
  id: number,
  parentId: number | null
): Promise<ApiResponseDto<DocumentFolderResponseDto>> => {
  return apiClient.put(`/v1/api/document-folders/${id}/move`, { parentId });
};

/**
 * Sao chép thư mục
 */
export const duplicateDocumentFolder = async (
  id: number,
  name?: string,
  parentId?: number | null
): Promise<ApiResponseDto<DocumentFolderResponseDto>> => {
  return apiClient.post(`/v1/api/document-folders/${id}/duplicate`, {
    name,
    parentId,
  });
};

/**
 * Lấy đường dẫn thư mục
 */
export const getDocumentFolderPath = async (
  id: number
): Promise<ApiResponseDto<DocumentFolderResponseDto[]>> => {
  return apiClient.get(`/v1/api/document-folders/${id}/path`);
};

/**
 * Lấy thư mục con
 */
export const getDocumentFolderChildren = async (
  id: number,
  params?: DocumentFolderQueryDto
): Promise<ApiResponseDto<PaginatedResult<DocumentFolderResponseDto>>> => {
  return apiClient.get(`/v1/api/document-folders/${id}/children`, { params });
};

/**
 * Kiểm tra quyền truy cập thư mục
 */
export const checkDocumentFolderPermission = async (
  id: number
): Promise<ApiResponseDto<{
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canManage: boolean;
}>> => {
  return apiClient.get(`/v1/api/document-folders/${id}/permissions`);
};
